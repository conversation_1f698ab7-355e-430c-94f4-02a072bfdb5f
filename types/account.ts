export type TAccount = {
  canTrade?: boolean;
  canWithdraw?: boolean;
  canDeposit?: boolean;
  balances: TBalance[];
};

export type TBalance = {
  asset: string;
  available: string;
  locked: string;
  operationId?: number;
};

export type TAccountUpdate = {
  asset: string;
  available: string;
  balance: string;
  email: string | null;
  id: number;
  locked: string;
  user_id: number;
};

export type TAccountUpdateWsData = {
  id: number;
  userId: number;
  asset: string;
  balance: string;
  available: string;
  locked: string;
  email: string | null;
  operationId: number;
};
