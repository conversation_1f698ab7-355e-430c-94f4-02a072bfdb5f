"use client";

import {
  LogoHeader,
  SearchIcon,
  WalletIcon,
  ImportIcon,
  ProfileIcon,
  ChevronDownIcon,
  GlobalIcon,
  BellIcon,
  MenuIcon,
  LogOutIcon,
  AddUserIcon,
  HomeIcon,
  OrderHistoryIcon,
  SettingIcon,
  WalletIconSmall,
  ProfileIconSmall,
  SettingIcon16,
} from "@/assets/icons";
import { AppButton } from "@/components";
import Link from "next/link";
import Image from "next/image";
import React, { useEffect, useRef, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { RootState } from "@/store/index";
import { clearUser } from "@/store/user.store";
import rf from "@/services/RequestFactory";
import { errorMsg } from "@/libs/toast";
import { maskEmail } from "../utils/format";
import useAccount from "@/hooks/useAccount";
import { usePathname } from "next/navigation";
import { ModalSettingLayout } from "../modals/ModalSettingLayout";

const MENUS_PROFILE = [
  {
    name: "Dashboard",
    path: "/my/dashboard",
    icon: <HomeIcon />,
  },
  {
    name: "Assets",
    icon: <WalletIcon />,
    path: "/my/overview",
  },
  {
    name: "Orders",
    icon: <OrderHistoryIcon />,
    path: "/my/orders-exchange",
  },
  {
    name: "Referral",
    icon: <AddUserIcon />,
    path: "/my/referral",
  },
  {
    name: "Account",
    icon: <ProfileIcon />,
    path: "#",
  },
  {
    name: "Settings",
    icon: <SettingIcon />,
    path: "/my/settings",
  },
];

const MENUS = [
  {
    name: "Buy Crypto",
    path: "/buy",
  },
  {
    name: "Trade",
    sub: [
      {
        name: "Overview",
        path: "/my/overview",
      },
      {
        name: "Spot",
        path: "#",
      },
    ],
  },
  {
    name: "More",
    sub: [
      {
        name: "Overview",
        path: "/my/overview",
      },
      {
        name: "Spot",
        path: "#",
      },
    ],
  },
];

const SubMenu = ({ menu }: { menu: any }) => {
  const [isShow, setIsShow] = useState(false);
  const contentRef = useRef<any>(null);

  const onToggleMenu = () => {
    setIsShow(!isShow);
  };

  const handleClickOutside = (event: Event) => {
    if (contentRef.current && !contentRef.current.contains(event.target)) {
      setIsShow(false);
    }
  };

  useEffect(() => {
    document.addEventListener("click", handleClickOutside, true);
    return () => {
      document.removeEventListener("click", handleClickOutside, true);
    };
  }, []);

  return (
    <div className="relative">
      <div
        onClick={onToggleMenu}
        className="block flex cursor-pointer items-center gap-2"
      >
        {menu?.name}{" "}
        <ChevronDownIcon
          className={`${
            isShow ? "rotate-[180deg]" : ""
          } 'duration-[600]' transition-all`}
        />
      </div>

      {isShow && (
        <div
          ref={contentRef}
          style={{
            boxShadow: "4px 4px 8px 0px var(--Black-500, rgba(8, 9, 12, 0.50))",
            background:
              "linear-gradient(0deg, var(--White-100, rgba(255, 255, 255, 0.10)) 0%, var(--White-100, rgba(255, 255, 255, 0.10)) 100%), var(--Black-900, #08090C)",
          }}
          className="z-9999 absolute left-0 top-[120%] min-w-[80px] rounded-[8px] p-1"
        >
          <div className="flex flex-col">
            {menu?.sub?.map((item: any, index: number) => {
              return (
                <Link
                  onClick={() => {
                    setIsShow(false);
                  }}
                  key={index}
                  href={item?.path}
                >
                  <div className="body-sm-regular-12 text-white-800 hover:text-white-1000 cursor-pointer p-2 px-[6px]">
                    {item?.name}
                  </div>
                </Link>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};

const MenuProfile = () => {
  const [isShow, setIsShow] = useState(false);
  const userInfo = useSelector((state: RootState) => state.user.userInfo);
  const contentRef = useRef<any>(null);
  const dispatch = useDispatch();

  const onToggleMenu = () => {
    setIsShow(!isShow);
  };

  const handleClickOutside = (event: Event) => {
    if (contentRef.current && !contentRef.current.contains(event.target)) {
      setIsShow(false);
    }
  };

  const onLogout = async () => {
    try {
      const response = await rf.getRequest("AuthRequest").logout();
      if (response?.success) {
        dispatch(clearUser());
        setIsShow(false);
        window.location.href = "/login";
      }
    } catch (error) {
      console.log(error, "handleLogin error");
      errorMsg("Logout error");
    }
  };

  useEffect(() => {
    document.addEventListener("click", handleClickOutside, true);
    return () => {
      document.removeEventListener("click", handleClickOutside, true);
    };
  }, []);

  return (
    <div className="relative">
      <div
        onClick={onToggleMenu}
        className="text-white-800 hover:text-white-1000 cursor-pointer p-2"
      >
        <ProfileIconSmall />
      </div>

      {isShow && (
        <div
          ref={contentRef}
          style={{
            boxShadow: "4px 4px 8px 0px var(--Black-500, rgba(8, 9, 12, 0.50))",
            background:
              "linear-gradient(0deg, var(--White-100, rgba(255, 255, 255, 0.10)) 0%, var(--White-100, rgba(255, 255, 255, 0.10)) 100%), var(--Black-900, #08090C)",
          }}
          className="z-9999 border-white-100 absolute right-0 top-[120%] min-w-[240px] rounded-[8px] border"
        >
          <div className="flex flex-col">
            <div className="border-white-100 flex items-center gap-2 border-b px-4 py-2">
              {userInfo.avatar ? (
                <img
                  src={userInfo.avatar}
                  alt="avatar"
                  className="aspect-square h-[40px] !w-[40px] rounded-[8px]"
                />
              ) : (
                <Image
                  src={"/images/AvatarDefault.png"}
                  alt="avatar"
                  width={40}
                  height={40}
                  className="aspect-square h-[40px] !w-[40px] rounded-[8px]"
                />
              )}

              <div className="flex-1">
                <div className="heading-sm-semibold-16 mb-1">
                  {maskEmail(userInfo.email || "")}
                </div>
                <div className="body-xs-medium-10 bg-white-100 border-white-100 mb-1 w-max rounded-[4px] border px-1">
                  Regular User
                </div>
              </div>
            </div>

            <div className="flex gap-2 px-4 py-2">
              <div className="body-xs-medium-10 mb-1 w-max rounded-[4px] border border-green-900 bg-green-900 px-1 text-green-500">
                Verified
              </div>
              <div className="body-xs-medium-10 bg-white-100 border-white-100 mb-1 w-max rounded-[4px] border px-1">
                Linked
              </div>
            </div>

            {MENUS_PROFILE.map((item: any, index: number) => {
              return (
                <Link
                  href={item.path || "#"}
                  key={index}
                  onClick={() => setIsShow(false)}
                >
                  <div
                    className={`hover:text-white-1000 text-white-500 flex items-center gap-1 px-4 py-2.5`}
                  >
                    <div>{item.icon}</div>
                    <div className={`action-md-medium-14 `}>{item.name}</div>
                  </div>
                </Link>
              );
            })}

            <div
              onClick={onLogout}
              className="body-md-medium-14 border-white-100 flex cursor-pointer gap-1 border-t px-4 py-2.5 text-red-400"
            >
              <LogOutIcon /> Log Out
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export const Header = () => {
  const accessToken = useSelector((state: RootState) => state.user.accessToken);
  useAccount();
  const pathname = usePathname();
  const isPairDetails = pathname?.includes("/trade/") || false;
  const [isShowModalSettingLayout, setIsShowModalSettingLayout] =
    useState<boolean>(false);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const _renderContentAuth = () => {
    if (!accessToken) {
      return (
        <div className="flex items-center gap-2">
          <Link href="/login">
            <AppButton variant="secondary" className="h-[32px] w-[80px]">
              Login
            </AppButton>
          </Link>
          <Link href="/register">
            <AppButton variant="buy" className="h-[32px] w-[80px]">
              Sign Up
            </AppButton>
          </Link>
        </div>
      );
    }

    return (
      <>
        <Link href={"/my/deposit"}>
          <AppButton variant="buy" className="gap-1 py-1">
            <ImportIcon />
            Deposit
          </AppButton>
        </Link>

        <div className="-mr-2 flex items-center">
          <MenuProfile />
          <div className="text-white-800 hover:text-white-1000 hidden cursor-pointer p-2 lg:block">
            <WalletIconSmall />
          </div>
          <div className="text-white-800 hover:text-white-1000 hidden cursor-pointer p-2 lg:block">
            <BellIcon />
          </div>
        </div>
      </>
    );
  };

  return (
    <>
      <div
        className={`border-white-100 bg-black-900 fixed left-0 right-0 top-0 z-[999] flex h-[50px] w-full items-center justify-between border-b px-4 ${
          isPairDetails ? "md:bg-[#151618]" : "bg-black-900"
        }`}
      >
        <div className="flex items-center gap-4">
          <Link href="/">
            <LogoHeader />
          </Link>
          <div className="hidden items-center gap-3 lg:flex">
            {MENUS.map((item: any, index) => {
              if (item?.sub) {
                return <SubMenu menu={item} key={index} />;
              }

              if (item?.path) {
                return (
                  <Link href={item?.path} key={index}>
                    <div
                      className={`body-md-medium-14 hover:text-white-1000 cursor-pointer px-[4px] py-3`}
                    >
                      {item.name}
                    </div>
                  </Link>
                );
              }
            })}
          </div>
        </div>

        <div className="flex items-center gap-2">
          <div className="text-white-800 hover:text-white-1000 hidden cursor-pointer p-2 lg:block">
            <SearchIcon />
          </div>

          {_renderContentAuth()}

          <div className="flex items-center">
            <div
              onClick={() => setIsShowModalSettingLayout(true)}
              className="text-white-800 hover:text-white-1000 hidden cursor-pointer p-2 lg:block"
            >
              <SettingIcon16 />
            </div>

            <div className="text-white-800 hover:text-white-1000 hidden cursor-pointer p-2 lg:block">
              <GlobalIcon />
            </div>
            <div className="cursor-pointer p-2 lg:hidden ">
              <MenuIcon />
            </div>
          </div>
        </div>
      </div>

      {mounted && (
        <ModalSettingLayout
          isOpen={isShowModalSettingLayout}
          onClose={() => setIsShowModalSettingLayout(false)}
        />
      )}
    </>
  );
};
