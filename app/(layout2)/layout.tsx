"use client";

import React, { ReactNode } from "react";
import { Header, Sidebar } from "@/layouts";
import {
  AddUserIcon,
  HomeIcon,
  OrderHistoryIcon,
  ProfileIcon,
  SettingIcon,
  WalletIcon,
} from "@/assets/icons";

const MENUS = [
  {
    name: "Dashboard",
    path: "/my/dashboard",
    icon: <HomeIcon />,
  },
  {
    name: "Assets",
    icon: <WalletIcon />,
    sub: [
      {
        name: "Overview",
        path: "/my/overview",
      },
      {
        name: "Spot",
        path: "#",
      },
    ],
  },
  {
    name: "Orders",
    icon: <OrderHistoryIcon />,
    sub: [
      {
        name: "Spot Orders",
        path: "/my/orders-exchange",
      },
      {
        name: "Transaction History",
        path: "/my/transaction-history",
      },
      {
        name: "Convert History",
        path: "/my/convert-history",
      },
    ],
  },
  {
    name: "Referral",
    icon: <AddUserIcon />,
    path: "/my/referral",
  },
  {
    name: "Account",
    icon: <ProfileIcon />,
    sub: [
      {
        name: "Identification",
        path: "/my/identification",
      },
      {
        name: "Security",
        path: "#",
      },
      {
        name: "Payment",
        path: "/my/payment",
      },
      {
        name: "API Management",
        path: "#",
      },
      {
        name: "Account Statement",
        path: "#",
      },
      {
        name: "Financial Reports",
        path: "/my/financial-reports",
      },
    ],
  },
  {
    name: "Settings",
    icon: <SettingIcon />,
    path: "/my/settings",
  },
];

export default function Layout({ children }: { children: ReactNode }) {
  return (
    <div>
      <Header />
      <div className="bg-black-900 min-h-screen pt-[50px]">
        <div className="mx-auto flex w-full max-w-[1440px] flex-col lg:flex-row lg:gap-6 lg:px-10 lg:py-6">
          <Sidebar menus={MENUS} />
          <div className="lg:flex-1">{children}</div>
        </div>
      </div>
    </div>
  );
}
