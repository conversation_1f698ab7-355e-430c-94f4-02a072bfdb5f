"use client";

import { AppButton } from "@/components";
import React from "react";
import Countdown from "react-countdown";

const ROUNDS = [
  {
    name: "R1",
    value: 50,
  },
  {
    name: "R2",
    value: 80,
  },
  {
    name: "R3",
    value: 120,
  },
  {
    name: "R4",
    value: 250,
  },
  {
    name: "R5",
    value: 500,
  },
  {
    name: "R6",
    value: 1000,
  },
];

const CountdownLock = ({
  time,
  onComplete,
}: {
  time: number;
  onComplete?: () => void;
}) => {
  const renderer = ({ days, hours, minutes, completed }: any) => {
    if (completed) {
      return "0";
    }

    return (
      <div className="heading-sm-medium-16 text-white-500 flex gap-2">
        <div className="heading-sm-semibold-16 text-white-1000">
          {days} <span className="heading-sm-medium-16 text-white-500">D</span>
        </div>{" "}
        :
        <div className="heading-sm-semibold-16 text-white-1000">
          {hours} <span className="heading-sm-medium-16 text-white-500">H</span>
        </div>{" "}
        :
        <div className="heading-sm-semibold-16 text-white-1000">
          {minutes}{" "}
          <span className="heading-sm-medium-16 text-white-500">M</span>
        </div>
      </div>
    );
  };

  return <Countdown date={time} renderer={renderer} onComplete={onComplete} />;
};

export default function ReferralPage() {
  return (
    <div className="p-4 lg:p-0">
      <div className="text-center text-[24px] font-semibold leading-[1.2] lg:text-[40px]">
        EARN TOGETHER
      </div>

      <div className="text-white-500 mb-4 mt-2 text-center text-[14px] font-medium leading-[1.5] lg:text-[16px]">
        Invite Friends to Unlock{" "}
        <span className="text-white-1000 font-semibold">2,000 USDC</span> Each
      </div>

      <div className="flex h-[333px] w-full flex-col items-center gap-4 bg-[url('/images/BgReferralMobile.png')] bg-cover bg-center bg-no-repeat py-4 lg:h-[408px] lg:bg-[url('/images/BgReferralDesktop.png')] lg:py-8">
        <div className="flex w-full gap-1 px-4 lg:w-[600px] lg:gap-2">
          {ROUNDS.map((item, index: number) => {
            const isCompleteR1 = index === 0;

            return (
              <div
                key={index}
                className={`flex items-center gap-1 lg:gap-2 ${
                  index !== ROUNDS.length - 1 ? "w-full " : ""
                }`}
              >
                <div className="flex flex-col items-center">
                  <div
                    className={`${
                      isCompleteR1 ? "bg-brand-500" : "bg-white-100"
                    } h-5 w-5 rotate-[45deg] rounded-[2px] text-center`}
                  >
                    <div className="body-xs-medium-10 mt-[2px] rotate-[-45deg] text-center">
                      {item.name}
                    </div>
                  </div>

                  <div className="body-sm-semibold-12 text-white-500 mt-2">
                    ${item.value}
                  </div>
                </div>

                {index !== ROUNDS.length - 1 && (
                  <div className="bg-white-500 -mt-[23px] h-[1px] w-full lg:mt-0 lg:w-full" />
                )}
              </div>
            );
          })}
        </div>

        <div className="flex flex-col items-center">
          <div className="text-brand-500 text-[64px] font-semibold leading-[1.2] lg:text-[80px]">
            94.74%
          </div>

          <div className="body-md-regular-14 text-white-500 my-2 lg:my-4">
            You&apos; ve accumulated
          </div>

          <div className="heading-lg-semibold-32">$47.373</div>
        </div>

        <div className="flex gap-2">
          <AppButton variant="outline" size={"large"} className="w-[124px]">
            Withdraw
          </AppButton>
          <AppButton variant="buy" size={"large"} className="w-[124px]">
            Invite Friends
          </AppButton>
        </div>

        <div className="flex items-center gap-2">
          <div className="body-md-regular-14 text-white-500">
            Current round time left:
          </div>
          <CountdownLock time={1749527031000} />
        </div>
      </div>

      <div className="mt-8 flex flex-col gap-4">
        <div>
          <div className="text-center text-[24px] font-semibold leading-[1.2] lg:text-[40px]">
            TASKS
          </div>
          <div className="text-white-500 mt-2 text-center text-[14px] font-medium leading-[1.5] lg:text-[16px]">
            Complete tasks to unlock the reward
          </div>
        </div>

        <div className="bg-white-50 w-full rounded-[8px] p-4">
          <div className="flex w-full flex-col items-start justify-between gap-4 lg:flex-row">
            <div>
              <div className="text-brand-500 heading-md-medium-18 mb-2">
                {`Task 1: Invite Friends to Trade > $100`}
              </div>
              <ul className="text-white-500 body-md-regular-14 list-disc pl-6">
                <li>
                  Invite friends to register and cumulatively trade more than{" "}
                  <span className="text-white-1000 font-semibold">$100</span>{" "}
                  via Spot or Convert to get a one-time boost.
                </li>
                <li>
                  Ensure you have an ongoing round before your friends register
                  and your friends complete the tasks during the same round that
                  they&apos;re invited, Otherwise, there will be no boost from their
                  task completion.
                </li>
              </ul>
            </div>

            <AppButton variant="buy" size="large" className="w-full lg:w-max">
              Invite
            </AppButton>
          </div>
        </div>

        <div className="bg-white-50 w-full rounded-[8px] p-4">
          <div className="flex w-full flex-col  items-start justify-between gap-4 lg:flex-row">
            <div>
              <div className="text-brand-500 heading-md-medium-18 mb-2">
                {`Task 2: Invite Friends to Trade >$300`}
              </div>
              <ul className="text-white-500 body-md-regular-14 list-disc pl-6">
                <li>
                  Invite friends to register and cumulatively trade more than{" "}
                  <span className="text-white-1000 font-semibold">$300</span>{" "}
                  via Spot or Convert to get a one-time boost.
                </li>
                <li>
                  Ensure you have an ongoing round before your friends register
                  and your friends complete the tasks during the same round that
                  they&apos;re invited, Otherwise, there will be no boost from their
                  task completion.
                </li>
              </ul>
            </div>

            <AppButton variant="buy" size="large" className="w-full lg:w-max">
              Invite
            </AppButton>
          </div>
        </div>

        <div className="bg-white-50 w-full rounded-[8px] p-4">
          <div className="flex w-full flex-col items-start justify-between gap-4 lg:flex-row">
            <div>
              <div className="text-brand-500 heading-md-medium-18 mb-2">
                {`Task 3: Invite Friends to Trade >$1000`}
              </div>
              <ul className="text-white-500 body-md-regular-14 list-disc pl-6">
                <li>
                  Invite friends to register and cumulatively trade more than{" "}
                  <span className="text-white-1000 font-semibold">$1000</span>{" "}
                  via Spot or Convert to get a one-time boost.
                </li>
                <li>
                  Ensure you have an ongoing round before your friends register
                  and your friends complete the tasks during the same round that
                  they&apos;re invited, Otherwise, there will be no boost from their
                  task completion.
                </li>
              </ul>
            </div>

            <AppButton variant="buy" size="large" className="w-full lg:w-max">
              Invite
            </AppButton>
          </div>
        </div>
      </div>

      <div className="mt-8">
        <div className="mb-4 text-center text-[24px] font-semibold leading-[1.2] lg:text-[40px]">
          AVAILABLE REWARDS
        </div>

        <div className="bg-white-50 w-full rounded-[8px] p-4">
          <div className="flex w-full items-start justify-between gap-4">
            <div>
              <div className="text-brand-500 heading-md-medium-18 mb-2">
                2000 USDC in Tokens
              </div>
              <ul className="text-white-500 body-md-regular-14 list-disc pl-6">
                <li>
                  Complete up to{" "}
                  <span className="text-white-1000 font-semibold">
                    6 rounds
                  </span>{" "}
                  to unlock up to{" "}
                  <span className="text-white-1000 font-semibold">
                    2,000 USDC
                  </span>{" "}
                  in token vouchers.
                </li>
                <li>
                  Limited rewards with a total prize pool of{" "}
                  <span className="text-white-1000 font-semibold">
                    500,000 USDC
                  </span>
                  , first come, first served.
                </li>
              </ul>
            </div>

            <AppButton variant="buy" size="large" className="hidden lg:block">
              Invite
            </AppButton>
          </div>

          <div className="bg-white-100 mt-4 flex flex-row items-center justify-between rounded-[8px] px-4 py-3 lg:flex-col lg:items-start lg:p-4">
            <div className="body-md-regular-14 text-white-500">
              Distributed rewards:
            </div>
            <div className="heading-md-medium-18 lg:mt-1">$1,242.42</div>
          </div>

          <AppButton
            variant="buy"
            size="large"
            className="mt-4 block w-full lg:hidden"
          >
            Invite
          </AppButton>
        </div>
      </div>
    </div>
  );
}
