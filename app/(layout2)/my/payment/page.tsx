"use client";

import React, { useState } from "react";
import { AppButton, AppPopover } from "@/components";
import { PlusIcon, VietNamFlagIcon, BankIcon } from "@/assets/icons";
import Image from "next/image";

enum PAYMENT_TYPE {
  P2P,
  CRYPTO,
}

const PAYMENT_METHOD = [
  {
    name: "Bank Transfer",
    icon: <BankIcon />,
  },
  {
    name: "Bank Transfer (Vietnam)",
    icon: <VietNamFlagIcon />,
  },
  {
    name: "Momo",
    icon: (
      <Image
        src="/images/payment/MomoIcon.svg"
        width={16}
        height={16}
        alt="Momo"
        unoptimized
      />
    ),
  },
  {
    name: "Viettel Money",
    icon: (
      <Image
        src="/images/payment/ViettelPayIcon.svg"
        width={16}
        height={16}
        alt="ViettelPay"
        unoptimized
      />
    ),
  },
  {
    name: "Zalo Pay",
    icon: (
      <Image
        src="/images/payment/ZaloPayIcon.svg"
        width={16}
        height={16}
        alt="ZaloPay"
        unoptimized
      />
    ),
  },
  {
    name: "VNPAY",
    icon: (
      <Image
        src="/images/payment/VNPAYIcon.svg"
        width={16}
        height={16}
        alt="VNPAY"
        unoptimized
      />
    ),
  },
];

const ButtonAddPaymentMethod = () => {
  const [isShow, setIsShow] = useState<boolean>(false);
  return (
    <AppPopover
      position="custom"
      customPosition={"left-0 top-[calc(100%+4px)]"}
      trigger={
        <AppButton
          variant="secondary"
          size="large"
          className="flex w-max items-center gap-2"
        >
          <PlusIcon /> Add a payment method
        </AppButton>
      }
      onClose={() => setIsShow(false)}
      content={
        <div
          style={{
            boxShadow: "4px 4px 8px 0px var(--Black-500, rgba(8, 9, 12, 0.50))",
            background:
              "linear-gradient(0deg, var(--White-100, rgba(255, 255, 255, 0.10)) 0%, var(--White-100, rgba(255, 255, 255, 0.10)) 100%), var(--Black-900, #08090C)",
          }}
          className="flex min-w-[220px] flex-col rounded-[8px] p-1"
        >
          <div>
            {PAYMENT_METHOD.map((item: any, index: number) => {
              return (
                <div
                  key={index}
                  className="body-sm-regular-12 flex cursor-pointer gap-2 px-1.5 py-2"
                >
                  {item.icon}
                  {item.name}
                </div>
              );
            })}
          </div>
        </div>
      }
      isOpen={isShow}
      onToggle={() => setIsShow(!isShow)}
    />
  );
};

export default function PaymentPage() {
  const [tabActive, setTabActive] = useState<number>(PAYMENT_TYPE.P2P);

  const _renderContentTab = () => {
    if (tabActive === PAYMENT_TYPE.P2P) {
      return (
        <div>
          <div className="body-md-regular-14 text-white-500 mb-4">
            2P payment methods: When you sell cryptocurrencies, the payment
            method added will be displayed to buyer as options to accept
            payment, please ensure that the account owner’s name is consistent
            with your verified name on Binance. You can add up to 20 payment
            methods.
          </div>

          <ButtonAddPaymentMethod />
        </div>
      );
    }

    return (
      <div className="body-md-regular-14 text-white-500">
        Manage the payment method of your credit and debit card on the buy
        crypto page
      </div>
    );
  };

  return (
    <div className="border-white-100 items-center justify-between gap-4 rounded-[16px] p-4 lg:border">
      <div className="text-white-500">
        <div className="heading-lg-medium-24 lg:text-white-500 text-white-1000">
          Payment
        </div>
      </div>

      <div className="border-white-50 flex border-b">
        <div
          onClick={() => setTabActive(PAYMENT_TYPE.P2P)}
          className={`body-md-medium-14 cursor-pointer px-4 py-2.5 ${
            tabActive === PAYMENT_TYPE.P2P
              ? "text-white-1000 border-white-500 border-b"
              : "text-white-500"
          }`}
        >
          P2p
        </div>
        <div
          onClick={() => setTabActive(PAYMENT_TYPE.CRYPTO)}
          className={`body-md-medium-14 cursor-pointer px-4 py-2.5 ${
            tabActive === PAYMENT_TYPE.CRYPTO
              ? "text-white-1000 border-white-500 border-b"
              : "text-white-500"
          }`}
        >
          Buy Crypto
        </div>
      </div>

      <div className="mt-4">{_renderContentTab()}</div>
    </div>
  );
}
