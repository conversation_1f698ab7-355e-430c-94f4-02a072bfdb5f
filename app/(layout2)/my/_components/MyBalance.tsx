import React, { useEffect, useState } from "react";
import { useMediaQuery } from "react-responsive";
import {
  InforIcon,
  EyeIcon,
  EyeCloseIcon,
  ChevronDownIcon,
  LineChartIcon,
} from "@/assets/icons";
import Tooltip from "rc-tooltip";
import { AppButton } from "@/components";
import Link from "next/link";
import { ModalDeposit } from "@/modals";
import {
  Area,
  AreaChart,
  CartesianGrid,
  ResponsiveContainer,
  XAxis,
  // YAxis,
  Tooltip as Tooltip<PERSON><PERSON>,
} from "recharts";
import { formatUnixTimestamp } from "@/utils/format";

const DATA_CHART = [
  {
    date: 1741539200,
    totalBalanceUsd: "15",
  },
  {
    date: 1745625600,
    totalBalanceUsd: "26",
  },
  {
    date: 1747625600,
    totalBalanceUsd: "10",
  },
  {
    date: 1787625600,
    totalBalanceUsd: "20",
  },
];

const RESOLUTIONS = ["1w", "1m", "3m", "6m"];

interface CustomTooltipProps {
  active?: boolean;
  payload?: Array<{
    value: number;
    payload: { value: number; timestamp: number };
  }>;
}

const CustomTooltip = ({ active, payload }: CustomTooltipProps) => {
  if (active && payload && payload.length) {
    return (
      <div className="border-white-50 body-2xs-regular-8 rounded-[4px] border bg-[#0f1018] px-1 py-[2px]">
        ${payload[0].value}
      </div>
    );
  }
  return null;
};

export const MyBalance = () => {
  const [isShowBalance, setIsShowBalance] = useState<boolean>(true);
  const [isShowChart, setIsShowChart] = useState<boolean>(false);
  const [isMounted, setIsMounted] = useState<boolean>(false);
  const [isShowModalDeposit, setIsShowModalDeposit] = useState<boolean>(false);
  const [resolution, setResolution] = useState<string>("1m");

  const isMobile = useMediaQuery({ query: "(max-width: 992px)" });

  useEffect(() => {
    setIsMounted(true);
  }, []);

  if (!isMounted) return <></>;

  const data = DATA_CHART.map((item) => ({
    time: formatUnixTimestamp(item.date * 1000, "MM-DD"),
    value: +item.totalBalanceUsd,
  }));

  return (
    <>
      <div className="border-white-100 flex flex-col gap-4 px-4  pt-2 lg:rounded-[16px] lg:border lg:pt-4">
        <div className="flex flex-col justify-between gap-4 lg:flex-row">
          <div className="flex flex-col gap-2">
            <div className="flex items-center justify-between">
              <div className="lg:heading-sm-medium-16 text-white-500 flex items-center gap-2 text-[12px]">
                Estimated Balance
                <div
                  onClick={() => setIsShowBalance(!isShowBalance)}
                  className="cursor-pointer"
                >
                  {isShowBalance ? <EyeIcon /> : <EyeCloseIcon />}
                </div>
              </div>
              <LineChartIcon
                onClick={() => setIsShowChart(!isShowChart)}
                className={`block lg:hidden ${
                  isShowChart ? "text-green-500" : ""
                }`}
              />
            </div>

            <div>
              <div className=" flex gap-2">
                <div className="heading-lg-medium-24">
                  {isShowBalance ? "5,932.422432" : "******"}
                </div>

                <div className="body-xs-medium-10 flex cursor-pointer items-center gap-1">
                  USDT <ChevronDownIcon />
                </div>
              </div>

              <div className="body-md-regular-14 text-white-800">
                {isShowBalance ? "≈5,932.42" : "******"}
              </div>
            </div>
            <div className="flex items-center gap-2">
              <div className="body-md-regular-14 text-white-500 flex items-center gap-2">
                Today&apos;s PnL
                <Tooltip
                  placement="top"
                  overlay={
                    <div className="body-xs-regular-10 max-w-[185px]">
                      Today&apos;s PNL = Current asset total - Today&apos;s
                      initial asset total - Today&apos;s net transfer and
                      deposit.*The calculation of PNL currently does not include
                      data for Alpha tokens. The date is only for your
                      reference, there is no guarantee that the data is
                      absolutely accurate.
                    </div>
                  }
                >
                  <InforIcon className="h-4 w-4 cursor-pointer" />
                </Tooltip>
              </div>
              {isShowBalance ? (
                <div className="body-md-medium-14 text-green-500">
                  +$530.87 (1.69%)
                </div>
              ) : (
                <div className="body-md-medium-14 text-white-500">******</div>
              )}
            </div>
          </div>

          <div className="flex flex-col items-end justify-between">
            <div className="flex w-full gap-2 lg:w-max lg:gap-4">
              <Link
                href="/my/withdraw"
                className="block w-[calc(50%-4px)] lg:min-w-[145px]"
              >
                <AppButton
                  size={isMobile ? "medium" : "large"}
                  variant="secondary"
                  className="w-full"
                >
                  Withdraw
                </AppButton>
              </Link>

              <AppButton
                onClick={() => setIsShowModalDeposit(true)}
                size={isMobile ? "medium" : "large"}
                variant={isMobile ? "buy" : "secondary"}
                className="w-[calc(50%-4px)] lg:min-w-[145px]"
              >
                Deposit
              </AppButton>
            </div>
            {isShowChart && (
              <div className="hidden gap-2 lg:flex ">
                {RESOLUTIONS.map((item, index) => {
                  return (
                    <div
                      key={index}
                      onClick={() => setResolution(item)}
                      className={`body-xs-medium-10 cursor-pointer rounded-[4px] px-2 py-[5px] uppercase ${
                        item === resolution
                          ? "bg-brand-900 text-brand-500"
                          : "bg-white-50"
                      }`}
                    >
                      {item}
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </div>

        {isShowChart && (
          <ResponsiveContainer width="100%" height={190}>
            <AreaChart width={388} height={190} data={data}>
              <defs>
                <defs>
                  <linearGradient
                    id="colorGradient"
                    x1="0"
                    y1="0"
                    x2="0"
                    y2="1"
                  >
                    <stop
                      offset="0%"
                      stopColor="#5BD08D"
                      stopOpacity={0.741176}
                    />
                    <stop
                      offset="104.97%"
                      stopColor="#5BD08D"
                      stopOpacity={0}
                    />
                  </linearGradient>
                </defs>
              </defs>
              <XAxis
                tickLine={false}
                axisLine={false}
                dataKey="time"
                className="body-xs-regular-10"
                fill="#FFFFFF4D"
              />
              <Area
                // yAxisId="right"
                type="linear"
                dataKey="value"
                stroke="#5BD08D"
                fill="url(#colorGradient)"
              />
              <TooltipChart content={<CustomTooltip />} />
            </AreaChart>
          </ResponsiveContainer>
        )}

        <div
          className="border-white-100 mx-auto hidden cursor-pointer rounded-t-[6px] border border-b-0 px-2.5 py-1.5 lg:block"
          onClick={() => setIsShowChart(!isShowChart)}
        >
          <ChevronDownIcon className={isShowChart ? "rotate-[180deg]" : ""} />
        </div>
      </div>
      <ModalDeposit
        isOpen={isShowModalDeposit}
        onClose={() => setIsShowModalDeposit(false)}
      />
    </>
  );
};
