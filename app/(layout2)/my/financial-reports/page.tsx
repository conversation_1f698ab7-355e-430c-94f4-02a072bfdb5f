"use client";

import React from "react";
import { AppButton } from "@/components";
import { RefreshIcon } from "@/assets/icons";

export default function FinancialReportsPage() {
  return (
    <div className="border-white-100 flex lg:flex-row gap-4 flex-col items-center justify-between rounded-[16px] lg:border p-4">
      <div className="text-white-500">
        <div className="heading-lg-medium-24 lg:text-white-500 text-white-1000">Financial Report</div>
        <div className="body-md-regular-14 mt-2">
          There are no documents generated for you at this time.
        </div>
      </div>

      <AppButton
        variant="secondary"
        size="large"
        className="flex items-center gap-2 h-max"
      >
        <RefreshIcon /> Refresh
      </AppButton>
    </div>
  );
}
