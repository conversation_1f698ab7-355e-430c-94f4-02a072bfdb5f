"use client";

import React from "react";
import Image from "next/image";
import { useSelector } from "react-redux";
import { RootState } from "@/store/index";
import { AppButton } from "@/components";
import { SupportIcon, QuestionIcon, HeadphoneIcon } from "@/assets/icons";

export default function IdentificationPage() {
  const userInfo = useSelector((state: RootState) => state.user.userInfo);

  return (
    <div className="flex flex-col gap-8">
      <div className="flex gap-4 p-4">
        {userInfo.avatar ? (
          <img
            src={userInfo.avatar}
            alt="avatar"
            className="aspect-square h-[64px] !w-[64px] rounded-[8px]"
          />
        ) : (
          <Image
            src={"/images/AvatarDefault.png"}
            alt="avatar"
            width={64}
            height={64}
            className="aspect-square h-[64px] !w-[64px] rounded-[8px]"
          />
        )}
        <div>
          <div className="lg:heading-lg-medium-24 text-[16px]">
            {userInfo?.name}
          </div>

          <div className="flex items-center gap-2">
            <div className="body-md-medium-14">
              <span className="body-md-regular-14 text-white-500">UID:</span>{" "}
              {userInfo?.sub}
            </div>
            <div className="border-white-100 bg-white-100 text-white-900 body-xs-medium-10 rounded-[4px] border px-1">
              Unverified
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white-50 border-white-100 w-max rounded-[16px] border p-4">
        <div className="heading-lg-medium-24 mb-2">
          Complete Identity Verification
        </div>
        <div className="body-md-regular-14 text-white-500 max-w-[340px]">
          Complete identity verification to access all VDAX service
        </div>

        <AppButton variant="buy" size="large" className="my-3 w-full">
          Get Verified
        </AppButton>

        <div className="flex gap-2 justify-center">
          <div className="flex items-center gap-1 px-2 cursor-pointer">
            <HeadphoneIcon />
            <div className="body-xs-medium-10">Need Help?</div>
          </div>
          <div className="flex items-center gap-1 px-2 cursor-pointer">
            <QuestionIcon />
            <div className="body-xs-medium-10">Identify Verification FAQ</div>
          </div>
        </div>
      </div>
    </div>
  );
}
