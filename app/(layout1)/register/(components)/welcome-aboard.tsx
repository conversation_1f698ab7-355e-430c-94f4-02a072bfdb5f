"use client";

import { Welcome } from "@/assets/icons";
import { AppButton } from "@/components";
import { clearUser } from "@/store/user.store";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useDispatch } from "react-redux";

export const WelcomeAboard: React.FC = () => {
  const dispatch = useDispatch();
  const [isShowInviter, setIsShowInviter] = useState<boolean>(false);
  const [referralId, setReferralId] = useState<string>("");
  const router = useRouter();

  const onCompleteRegister = async () => {
    dispatch(clearUser());
    router.push("/login");
  };

  return (
    <>
      <div className="flex justify-center">
        <Welcome />
      </div>
      <div className="heading-lg-semibold-24 text-center lg:text-[32px]">
        Welcome aboard!
      </div>
      <div className="body-md-regular-14 text-center">
        Do you have an inviter? (Optional)
      </div>
      <div className="flex flex-col gap-4">
        <div className="grid grid-cols-2 gap-4">
          <AppButton
            variant="outline"
            size="large"
            className={isShowInviter ? "bg-white-100" : ""}
            onClick={() => setIsShowInviter(true)}
          >
            Yes
          </AppButton>

          <AppButton
            variant="outline"
            size="large"
            onClick={() => {
              setIsShowInviter(false);
              onCompleteRegister();
            }}
          >
            No
          </AppButton>
        </div>
        {isShowInviter && (
          <div className="border-white-100 flex w-full items-center gap-2 rounded-[6px] border px-2 py-3">
            <input
              className="body-sm-regular-12 placeholder:text-white-300 flex-1 outline-none"
              placeholder="Referral ID"
              value={referralId}
              onChange={(e) => setReferralId(e.target.value)}
            />
            {referralId && (
              <div className="text-brand-500 cursor-pointer">Linked</div>
            )}
          </div>
        )}
      </div>

      <AppButton variant="buy" size="large" onClick={onCompleteRegister}>
        Next
      </AppButton>
    </>
  );
};
