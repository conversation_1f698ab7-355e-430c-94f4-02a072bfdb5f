import React, { CSSProperties, memo } from "react";
import Slider from "rc-slider";
import "rc-slider/assets/index.css";

const marks = [0, 25, 50, 75, 100];

const getDotStyle = (dotValue: number, isActive: boolean): CSSProperties => {
  const isKeyPoint = marks.includes(Number(dotValue));
  const color = isActive ? "white" : "#38393a";
  const leftPosition = `calc(${dotValue}% - 4px)`;

  const commonStyles = {
    left: Number(dotValue) === 100 ? leftPosition : leftPosition,
  };

  if (!isKeyPoint) {
    return {
      ...commonStyles,
      backgroundColor: "transparent",
      border: "none",
    };
  }

  return {
    ...commonStyles,
    backgroundColor: color,
    transform: "rotate(45deg) translate(-50%, -50%)",
    height: "7px",
    width: "7px",
    position: "absolute",
    border: `1px solid ${color}`,
    borderRadius: "0px",
    bottom: "-5px",
  };
};

export const AppSlider = memo(
  ({
    value,
    handleChange,
    setValue,
  }: {
    value: number;
    handleChange: (value: number | string) => void;
    setValue: (value: number) => void;
  }) => {
    return (
      <div className="my-1 h-2 px-2">
        <Slider
          value={value}
          onChange={(value: number | number[]) => {
            const newValue = Array.isArray(value) ? value[0] : value;
            setValue(newValue);
            handleChange(newValue);
          }}
          styles={{
            track: {
              backgroundColor: "white",
              height: "2px",
            },
            rail: {
              backgroundColor: "#38393a",
              height: "2px",
            },
            handle: {
              backgroundColor: "white",
              border: "none",
              borderRadius: "0px",
              transform: "rotate(45deg) translate(-50%, -50%)",
              height: "7px",
              width: "7px",
              bottom: "0px",
              left: "-4px",
              boxShadow: "none",
            },
          }}
          dots={true}
          dotStyle={(dotValue: number) => getDotStyle(dotValue, false)}
          activeDotStyle={(dotValue: number) => getDotStyle(dotValue, true)}
        />
      </div>
    );
  }
);

AppSlider.displayName = "AppSlider";
