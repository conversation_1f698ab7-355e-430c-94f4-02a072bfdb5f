"use client";

import { memo, useCallback, useEffect, useState, useRef } from "react";
import { SearchIcon, StarIcon, HolderIcon, SwapIcon } from "@/assets/icons";
import { AppButtonSort } from "@/components/AppButtonSort";
import rf from "@/services/RequestFactory";
import { TPairMarket, TPairSetting, TradingPair } from "@/types/pair";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { useRouter } from "next/navigation";
import {
  getArrTickerRoom,
  subscribeSocketChannel,
  unsubscribeSocketChannel,
} from "@/libs/socket";
import {
  AppBroadcast,
  BROADCAST_EVENTS,
  TBroadcastEvent,
} from "@/libs/broadcast";
import { TTickerUpdate } from "./services/TickerHandler";
import { MarketPairRow } from "./components/MarketPairRow";

const TAB_KEYS = {
  FAVOURITE: "FAVOURITE",
};

const TABS = [
  {
    name: <StarIcon />,
    value: TAB_KEYS.FAVOURITE,
  },
];

export const MarketExplorer = memo(() => {
  // UI state
  const [search, setSearch] = useState<string>("");
  const [tabActive, setTabActive] = useState<string>("USDT");

  const [isShow24hChange, setIsShow24hChange] = useState<boolean>(true);
  const [sortBy, setSortBy] = useState<string>("");
  const [sortType, setSortType] = useState<string>("");

  // Data state
  const [pairMarkets, setPairMarkets] = useState<TPairMarket[]>([]);
  const [tradingPairs, setTradingPairs] = useState<TPairSetting[]>([]);
  const [tradingPairsFiltered, setTradingPairsFiltered] = useState<
    TPairSetting[]
  >([]);
  const [favoritePairs, setFavoritePairs] = useState<Set<string>>(new Set());

  const pairTickersRef = useRef<Record<string, TradingPair>>({});

  const [renderCount, setRenderCount] = useState<number>(0);
  const frameIdRef = useRef<number | null>(null);
  const router = useRouter();
  const socketConnected = useSelector(
    (state: RootState) => state.metadata.socketConnected
  );

  const userBalances = useSelector(
    (state: RootState) => state.account.account?.balances || []
  );

  useEffect(() => {
    const loadFavorites = () => {
      try {
        const storedFavorites = localStorage.getItem("favoritePairs");
        if (storedFavorites) {
          setFavoritePairs(new Set(JSON.parse(storedFavorites)));
        }
      } catch (error) {
        console.log("Error loading favorites:", error);
      }
    };

    loadFavorites();
  }, []);

  useEffect(() => {
    if (favoritePairs.size > 0) {
      localStorage.setItem("favoritePairs", JSON.stringify([...favoritePairs]));
    }
  }, [favoritePairs]);

  useEffect(() => {
    const handleToggleFavorite = (data: TBroadcastEvent) => {
      const { symbol } = data.detail;
      toggleFavorite(symbol);
    };
    AppBroadcast.on(BROADCAST_EVENTS.TOGGLE_FAVORITE, handleToggleFavorite);

    return () => {
      AppBroadcast.remove(
        BROADCAST_EVENTS.TOGGLE_FAVORITE,
        handleToggleFavorite
      );
    };
  }, []);

  const toggleFavorite = (symbol: string) => {
    setFavoritePairs((prev) => {
      const newFavorites = new Set(prev);
      if (newFavorites.has(symbol)) {
        newFavorites.delete(symbol);
      } else {
        newFavorites.add(symbol);
      }
      return newFavorites;
    });
  };

  useEffect(() => {
    const initPairMarket = async () => {
      try {
        const data = await rf.getRequest("PairRequest").getPairMarket();
        setPairMarkets(
          data?.data.map((item: TPairMarket) => ({ ...item, value: item.name }))
        );
      } catch (error) {
        console.log("init pair market error", error);
      }
    };

    const initTradingPairs = async () => {
      try {
        const data = await rf.getRequest("PairRequest").getTradingPairs();
        setTradingPairs(data?.data || []);
      } catch (error) {
        console.log("init trading pairs error", error);
      }
    };

    initPairMarket();
    initTradingPairs();
  }, []);

  useEffect(() => {
    if (!tradingPairs.length) return;

    let filtered = [...tradingPairs];

    if (tabActive === TAB_KEYS.FAVOURITE) {
      filtered = filtered.filter((pair) => favoritePairs.has(pair.symbol));
    } else {
      filtered = filtered.filter((pair) => {
        return pair.quoteAsset?.toUpperCase() === tabActive?.toUpperCase();
      });
    }

    if (search) {
      const searchLower = search.toLowerCase();
      filtered = filtered.filter((pair) =>
        pair.symbol.toLowerCase().includes(searchLower)
      );
    }

    if (sortBy && sortType) {
      // Sort by the selected column
      filtered.sort((a, b) => {
        const multiplier = sortType === "asc" ? 1 : -1;

        // Sort by pair name
        if (sortBy === "pair") {
          return multiplier * a.symbol.localeCompare(b.symbol);
        }

        // Sort by price
        if (sortBy === "price") {
          const priceA = parseFloat(
            pairTickersRef.current[a.symbol]?.lastPrice || "0"
          );
          const priceB = parseFloat(
            pairTickersRef.current[b.symbol]?.lastPrice || "0"
          );
          return multiplier * (priceA - priceB);
        }

        // Sort by 24h change
        if (sortBy === "24Change") {
          const changeA = parseFloat(
            pairTickersRef.current[a.symbol]?.priceChangePercent || "0"
          );
          const changeB = parseFloat(
            pairTickersRef.current[b.symbol]?.priceChangePercent || "0"
          );
          return multiplier * (changeA - changeB);
        }

        // Sort by volume
        if (sortBy === "Volume") {
          const volumeA = parseFloat(
            pairTickersRef.current[a.symbol]?.quoteVolume || "0"
          );
          const volumeB = parseFloat(
            pairTickersRef.current[b.symbol]?.quoteVolume || "0"
          );
          return multiplier * (volumeA - volumeB);
        }

        return 0;
      });
    }

    setTradingPairsFiltered(filtered);
  }, [
    tabActive,
    search,
    tradingPairs,
    favoritePairs,
    userBalances,
    sortBy,
    sortType,
    renderCount, // Include renderCount to re-run when ticker data changes
  ]);

  const handleArrTickersUpdate = useCallback((data: TBroadcastEvent) => {
    const tickersUpdate = JSON.parse(data.detail) as TTickerUpdate[];

    tickersUpdate.forEach((ticker) => {
      const symbol = ticker.s;

      // Update or create ticker data for this symbol in the ref
      if (!pairTickersRef.current[symbol]) {
        pairTickersRef.current[symbol] = {} as TradingPair;
      }

      pairTickersRef.current[symbol] = {
        lastPrice: ticker.c,
        highPrice: ticker.h,
        lowPrice: ticker.l,
        priceChangePercent: ticker.P,
        priceChange: ticker.p,
        baseVolume: ticker.v,
        quoteVolume: ticker.q,
        timestamp: ticker.E,
        isUp: ticker.p.startsWith("-") ? false : true,
        isUp24h: ticker.P.startsWith("-") ? false : true,
      };
    });

  }, []);

  

  // Subscribe to ticker updates
  useEffect(() => {
    if (!socketConnected) return;
    subscribeSocketChannel({
      params: [getArrTickerRoom()],
    });

    AppBroadcast.on(
      BROADCAST_EVENTS.ARR_TICKERS_UPDATED,
      handleArrTickersUpdate
    );

    return () => {
      // Clean up socket and event listeners
      unsubscribeSocketChannel({
        params: [getArrTickerRoom()],
      });

      AppBroadcast.remove(
        BROADCAST_EVENTS.ARR_TICKERS_UPDATED,
        handleArrTickersUpdate
      );
    };
  }, [handleArrTickersUpdate, socketConnected]);

  const handlePairClick = (symbol: string) => {
    router.replace(`/trade/${symbol}`);
  };

  return (
    <div className="border-white-100 h-[420px] border-b p-3">
      <div className="border-white-100 flex items-center gap-2 rounded-[6px] border px-2 py-2">
        <SearchIcon className="text-white-500" />
        <input
          className="body-md-regular-14 placeholder:text-white-300 bg-transparent outline-none"
          placeholder="Search"
          value={search}
          onChange={(e) => setSearch(e.target.value)}
        />
      </div>

      <div className="border-white-50 flex border-b">
        {[...TABS, ...pairMarkets].map((item: TPairMarket, index) => {
          return (
            <div
              onClick={() => setTabActive(item.value)}
              className={`flex h-[40px] cursor-pointer items-center px-3 py-2.5 ${
                item.value === tabActive
                  ? "text-white-1000 body-sm-regular-12 border-white-500 border-b"
                  : "text-white-500 body-sm-regular-12"
              }`}
              key={index}
            >
              {item.name}
            </div>
          );
        })}
      </div>

      <div>
        <div className="flex justify-between">
          <div className="body-sm-regular-12 text-white-500 flex items-center gap-2 p-2">
            Pair{" "}
            <AppButtonSort
              value="pair"
              sortBy={sortBy}
              sortType={sortType}
              setSortType={setSortType}
              setSortBy={setSortBy}
            />
          </div>
          <div className="body-sm-regular-12 text-white-500 flex items-center py-2 text-right">
            <div className="flex items-center gap-2">
              Last price
              <AppButtonSort
                value="price"
                sortBy={sortBy}
                sortType={sortType}
                setSortType={setSortType}
                setSortBy={setSortBy}
              />
            </div>
            <div className="ml-1 flex items-center gap-2">
              / {isShow24hChange ? "24h Change" : "Vol"}{" "}
              <AppButtonSort
                value={isShow24hChange ? "24Change" : "Volume"}
                sortBy={sortBy}
                sortType={sortType}
                setSortType={setSortType}
                setSortBy={setSortBy}
              />
            </div>{" "}
            <SwapIcon
              onClick={() => setIsShow24hChange(!isShow24hChange)}
              className="hover:text-white-1000 ml-0.5 cursor-pointer"
            />
          </div>
        </div>

        <div className="-mx-3">
          {/* Use memoized rows for better performance */}
          {tradingPairsFiltered?.map((item, index) => (
            <MarketPairRow
              key={item.symbol || index}
              item={item}
              ticker={pairTickersRef.current[item.symbol]}
              isShow24hChange={isShow24hChange}
              isFavorite={favoritePairs.has(item.symbol)}
              onToggleFavorite={toggleFavorite}
              onPairClick={handlePairClick}
            />
          ))}
        </div>
      </div>
    </div>
  );
});

MarketExplorer.displayName = "MarketExplorer";
