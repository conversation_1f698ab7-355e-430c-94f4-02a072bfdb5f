"use client";

import { <PERSON><PERSON><PERSON><PERSON>on, AppSlider } from "@/components";
import { InputAmount } from "../components/InputAmount";
import { useEffect, useState } from "react";
import { multipliedBN } from "@/utils/number";
import { EOrderType } from "..";
import AppNumber from "@/components/AppNumber";
import { ErrorMessages } from "@/types/errors";
import BigNumber from "bignumber.js";
import { EOrderSide } from "../OrderFormMobile";
import rf from "@/services/RequestFactory";
import { errorMsg } from "@/libs/toast";
import { useOrderToast } from "../hooks/useOrderMessage";
import { EOrderStatus, TOrderAction } from "@/types/order";
import { usePairContext } from "@/app/trade/[symbol]/provider";
import { isEmpty } from "lodash";
import useAccountBalance from "@/hooks/useAccountBalance";
import { isInvalidNumber } from "@/utils/helper";

type TOrderSell = {
  orderType: EOrderType;
  defaultPrice?: string;
  defaultAmount?: string;
};

export const SellOrderForm = ({
  orderType,
  defaultPrice,
  defaultAmount,
}: TOrderSell) => {
  const [price, setPrice] = useState<string>("");
  const [amount, setAmount] = useState<string>("");
  const [total, setTotal] = useState<string>("");
  const [maxSell, setMaxSell] = useState<string>("");
  const [sliderValue, setSliderValue] = useState<number>(0);
  const [errorMessage, setErrorMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const showOrderToast = useOrderToast();
  const { pairSetting } = usePairContext();
  const { coinBalance } = useAccountBalance({ coin: pairSetting?.baseAsset });

  // Handle errors
  useEffect(() => {
    if (BigNumber(amount).isGreaterThan(coinBalance.available)) {
      setErrorMessage(ErrorMessages.INVALID_BALANCE);
      return;
    }

    setErrorMessage("");
  }, [amount, coinBalance.available]);

  useEffect(() => {
    if (!defaultPrice) {
      setPrice("");
      return;
    }

    setPrice(defaultPrice);
    onChangePrice(defaultPrice);
  }, [defaultPrice]);

  useEffect(() => {
    if (
      !defaultAmount ||
      !coinBalance.available ||
      BigNumber(coinBalance.available).isZero()
    ) {
      setAmount("");
      return;
    }

    const correctAmount = BigNumber(coinBalance.available).isGreaterThan(
      defaultAmount
    )
      ? defaultAmount
      : coinBalance.available;

    setAmount(correctAmount);
    onChangeAmount(correctAmount);
  }, [defaultAmount]);

  useEffect(() => {
    if (!+price) {
      setMaxSell("");
      return;
    }

    const maxSellValue = multipliedBN(price, coinBalance.available);
    setMaxSell(maxSellValue);
  }, [price, coinBalance.available]);

  const onChangePrice = (value: string) => {
    if (isInvalidNumber(value)) {
      setPrice(value);
      setTotal("");
      return;
    }

    setPrice(value);
    const totalValue = multipliedBN(value, amount);
    setTotal(totalValue);
  };

  const onChangeAmount = (value: string) => {
    if (isInvalidNumber(value)) {
      setAmount(value);
      setTotal("");
      setSliderValue(0);
      return;
    }

    setAmount(value);
    // Calculate total
    const totalValue = multipliedBN(price, value);
    setTotal(totalValue);

    updateSliderValue(value);
  };

  const onChangeTotal = (value: string) => {
    if (isInvalidNumber(value)) {
      setTotal(value);
      setAmount("");
      return;
    }

    setTotal(value);
    const amountValue = BigNumber(value)
      .div(price)
      .toFixed(pairSetting?.quantityPrecision || 0, BigNumber.ROUND_DOWN);
    setAmount(amountValue);

    updateSliderValue(amountValue);
  };

  const updateSliderValue = (totalAmount: string) => {
    if (
      !coinBalance.available ||
      BigNumber(coinBalance.available).isLessThanOrEqualTo(0)
    ) {
      setSliderValue(0);
      return;
    }
    const newSliderValue = BigNumber(totalAmount)
      .div(coinBalance.available)
      .multipliedBy(100)
      .toFixed();

    setSliderValue(parseInt(newSliderValue));
  };

  const handlePlaceOrder = async () => {
    if (isEmpty(pairSetting)) {
      return;
    }

    setIsLoading(true);
    try {
      const orderParams = {
        symbol: pairSetting.symbol,
        side: EOrderSide.SELL,
        type: orderType,
        price,
        quantity: BigNumber(amount).toFixed(
          pairSetting?.quantityPrecision || 0
        ),
      };
      const res = await rf.getRequest("OrderRequest").placeOrder(orderParams);

      showOrderToast({
        baseAsset: pairSetting.baseAsset || "",
        quoteAsset: pairSetting.quoteAsset || "",
        action: TOrderAction.PLACE,
        quantity: res.orig_qty,
        status: EOrderStatus.NEW,
        type: orderType,
        side: EOrderSide.SELL,
      });
      setTotal("");
      setAmount("");
      setSliderValue(0);
    } catch (error: any) {
      errorMsg(`Error placing order ${error?.message || ""}`);
      console.log(error, "handlePlaceOrder error");
    } finally {
      setIsLoading(false);
    }
  };

  const onChangeSlider = (value: number | string) => {
    if (value === 0) {
      setTotal("");
      setAmount("");
      return;
    }

    const amountUpdated = BigNumber(value)
      .div(100)
      .multipliedBy(coinBalance.available)
      .toFixed(pairSetting?.quantityPrecision || 0, BigNumber.ROUND_DOWN);
    onChangeAmount(amountUpdated);
  };

  const isDisableBuyButton = !!errorMessage.length || +amount <= 0;

  return (
    <div className="flex flex-col gap-4">
      {orderType === EOrderType.LIMIT ? (
        <>
          <InputAmount
            value={price}
            label={"Price"}
            onChange={onChangePrice}
            prefix={pairSetting?.quoteAsset?.toUpperCase()}
            decimal={pairSetting?.pricePrecision}
            isHasArrowChange
          />

          <InputAmount
            value={amount}
            label={"Amount"}
            onChange={onChangeAmount}
            prefix={pairSetting?.baseAsset?.toUpperCase()}
            decimal={pairSetting?.quantityPrecision}
            isHasArrowChange
          />
        </>
      ) : (
        <div className="bg-white-50 flex rounded-[6px]">
          <div className="flex w-full cursor-not-allowed justify-between px-3 py-2">
            <div className="body-md-regular-14 text-white-500">Price</div>
            <div className="body-md-regular-14 text-white-500">
              Market price
            </div>
          </div>
        </div>
      )}

      <AppSlider
        handleChange={onChangeSlider}
        value={sliderValue}
        setValue={setSliderValue}
      />

      <InputAmount
        value={total}
        label={"Total"}
        onChange={onChangeTotal}
        placeholder="Minimum 5"
        prefix={pairSetting?.quoteAsset?.toUpperCase()}
      />

      {errorMessage && (
        <div className="body-sm-regular-12 text-red-500">{errorMessage}</div>
      )}

      <div className="flex flex-col gap-2">
        <div className="flex justify-between">
          <div className="body-sm-regular-12 text-white-500">Avbl</div>
          <div className="body-sm-medium-12 flex items-center gap-1">
            <AppNumber
              value={coinBalance.available}
              decimals={pairSetting?.quantityPrecision}
              isFormatLargeNumber={false}
            />{" "}
            {pairSetting?.baseAsset?.toUpperCase()}
          </div>
        </div>
        <div className="flex justify-between">
          <div className="body-sm-regular-12 text-white-500">Max Sell</div>
          <div className="body-sm-medium-12 flex items-center gap-1">
            <AppNumber
              value={maxSell}
              decimals={pairSetting?.quantityPrecision}
              isFormatLargeNumber={false}
            />{" "}
            {pairSetting?.quoteAsset?.toUpperCase()}
          </div>
        </div>
      </div>

      <AppButton
        variant="sell"
        size="large"
        onClick={handlePlaceOrder}
        disabled={isDisableBuyButton}
        isLoading={isLoading}
      >
        Sell
      </AppButton>
    </div>
  );
};
