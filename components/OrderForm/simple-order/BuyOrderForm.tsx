"use client";

import { <PERSON><PERSON><PERSON><PERSON>on, AppSlider } from "@/components";
import { useEffect, useMemo, useState } from "react";
import { InputAmount } from "../components/InputAmount";
import { EOrderType } from "..";
import { dividedBN, multipliedBN } from "@/utils/number";
import AppNumber from "@/components/AppNumber";
import BigNumber from "bignumber.js";
import { ErrorMessages } from "@/types/errors";
import rf from "@/services/RequestFactory";
import { EOrderSide } from "../OrderFormMobile";
import { useOrderToast } from "../hooks/useOrderMessage";
import { EOrderStatus, TOrderAction } from "@/types/order";
import { errorMsg } from "@/libs/toast";
import { usePairContext } from "@/app/trade/[symbol]/provider";
import { isEmpty } from "lodash";
import useAccountBalance from "@/hooks/useAccountBalance";
import { isInvalidNumber } from "@/utils/helper";

type TOrderBuy = {
  orderType: EOrderType;
  defaultPrice?: string;
  defaultAmount?: string;
};

export const BuyOrderForm = ({
  orderType,
  defaultPrice,
  defaultAmount,
}: TOrderBuy) => {
  const [price, setPrice] = useState<string>("");
  const [amount, setAmount] = useState<string>("");
  const [total, setTotal] = useState<string>("");
  const [maxBuy, setMaxBuy] = useState<string>("");
  const [sliderValue, setSliderValue] = useState<number>(0);
  const [errorMessage, setErrorMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const { pairSetting } = usePairContext();
  const showOrderToast = useOrderToast();
  const { coinBalance } = useAccountBalance({ coin: pairSetting?.quoteAsset });

  const onChangePrice = (value: string) => {
    if (isInvalidNumber(value)) {
      setPrice("");
      setTotal("");
      return;
    }

    setPrice(value);
    const totalValue = multipliedBN(value, amount);
    setTotal(totalValue);
  };

  const onChangeAmount = (value: string) => {
    if (isInvalidNumber(value)) {
      setAmount("");
      setTotal("");
      setSliderValue(0);
      return;
    }

    setAmount(value);
    const totalValue = multipliedBN(price, value);
    setTotal(totalValue);

    updateSliderValue(totalValue);
  };

  const onChangeTotal = (value: string) => {
    if (isInvalidNumber(value)) {
      setTotal("");
      setAmount("");
      setSliderValue(0);
      return;
    }

    setTotal(value);
    const amountValue = dividedBN(value, price);

    setAmount(
      BigNumber(amountValue).toFixed(
        pairSetting?.quantityPrecision || 0,
        BigNumber.ROUND_DOWN
      )
    );

    updateSliderValue(value);
  };

  const updateSliderValue = (currentBaseTotal: string) => {
    if (
      !coinBalance.available ||
      BigNumber(coinBalance.available).isLessThanOrEqualTo(0)
    ) {
      setSliderValue(0);
      return;
    }
    const newSliderValue = BigNumber(currentBaseTotal)
      .div(coinBalance.available)
      .multipliedBy(100)
      .toFixed();

    setSliderValue(parseInt(newSliderValue));
  };

  const handlePlaceOrder = async () => {
    if (isEmpty(pairSetting)) {
      return;
    }

    setIsLoading(true);
    try {
      const orderParams = {
        symbol: pairSetting.symbol,
        side: EOrderSide.BUY,
        type: orderType,
        quantity: BigNumber(amount).toFixed(
          pairSetting?.quantityPrecision || 0
        ),
      } as any;

      if (orderType === EOrderType.LIMIT) {
        orderParams.price = price;
      }

      const res = await rf.getRequest("OrderRequest").placeOrder(orderParams);

      showOrderToast({
        baseAsset: pairSetting.baseAsset,
        quoteAsset: pairSetting.quoteAsset,
        action: TOrderAction.PLACE,
        quantity: res.orig_qty,
        status: EOrderStatus.NEW,
        type: orderType,
        side: EOrderSide.BUY,
      });
      setTotal("");
      setAmount("");
      setSliderValue(0);
    } catch (error: any) {
      errorMsg(`Error placing order ${error?.message || ""}`);
      console.log(error, "handlePlaceOrder error");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (!+price) {
      setMaxBuy("");
      return;
    }

    const maxBuyValue = dividedBN(coinBalance.available, price);
    setMaxBuy(maxBuyValue);
  }, [price, coinBalance.available]);

  // Handle errors
  useEffect(() => {
    if (BigNumber(total).isGreaterThan(coinBalance.available)) {
      setErrorMessage(ErrorMessages.INVALID_BALANCE);
      return;
    }

    setErrorMessage("");
  }, [total, coinBalance.available]);

  useEffect(() => {
    if (!defaultPrice) {
      setPrice("");
      return;
    }

    setPrice(defaultPrice);
    onChangePrice(defaultPrice);
  }, [defaultPrice]);

  useEffect(() => {
    if (
      !defaultAmount ||
      !coinBalance.available ||
      BigNumber(coinBalance.available).isZero()
    ) {
      setAmount("");
      return;
    }

    const correctAmount = BigNumber(coinBalance.available).isGreaterThan(
      defaultAmount
    )
      ? defaultAmount
      : coinBalance.available;

    setAmount(correctAmount);
    onChangeAmount(correctAmount);
  }, [defaultAmount]);

  const onChangeSlider = (value: number | string) => {
    if (value === 0) {
      setTotal("");
      setAmount("");
      setSliderValue(0);
      return;
    }

    const total = BigNumber(value)
      .div(100)
      .multipliedBy(coinBalance.available)
      .toFixed();

    onChangeTotal(total);
  };

  const isDisableBuyButton = useMemo(() => {
    if (errorMessage.length > 0) {
      return true;
    }

    if (BigNumber(amount || 0).isLessThanOrEqualTo(0)) {
      return true;
    }

    if (
      orderType === EOrderType.LIMIT &&
      BigNumber(price || 0).isLessThanOrEqualTo(0)
    ) {
      return true;
    }

    return false;
  }, [errorMessage, amount, price, isLoading, orderType]);

  return (
    <div className="flex flex-col gap-4">
      {orderType === EOrderType.LIMIT ? (
        <>
          <InputAmount
            value={price}
            label={"Price"}
            onChange={onChangePrice}
            prefix={pairSetting?.quoteAsset?.toUpperCase()}
            decimal={pairSetting?.pricePrecision}
            isHasArrowChange
          />

          <InputAmount
            value={amount}
            label={"Amount"}
            onChange={onChangeAmount}
            prefix={pairSetting?.baseAsset?.toUpperCase()}
            decimal={pairSetting?.quantityPrecision}
            isHasArrowChange
          />
        </>
      ) : (
        <div className="bg-white-50 flex rounded-[6px]">
          <div className="flex w-full cursor-not-allowed justify-between px-3 py-2">
            <div className="body-md-regular-14 text-white-500">Price</div>
            <div className="body-md-regular-14 text-white-500">
              Market price
            </div>
          </div>
        </div>
      )}

      <AppSlider
        handleChange={onChangeSlider}
        value={sliderValue}
        setValue={setSliderValue}
      />

      <InputAmount
        value={total}
        label={"Total"}
        onChange={onChangeTotal}
        placeholder="Minimum 5"
        prefix={pairSetting?.quoteAsset?.toUpperCase()}
      />

      {errorMessage && (
        <div className="body-sm-regular-12 text-red-500">{errorMessage}</div>
      )}

      <div className="flex flex-col gap-2">
        <div className="flex justify-between">
          <div className="body-sm-regular-12 text-white-500">Avbl</div>
          <div className="body-sm-medium-12 flex items-center gap-1">
            <AppNumber
              value={coinBalance.available}
              decimals={pairSetting?.quantityPrecision}
              isFormatLargeNumber={false}
            />{" "}
            {pairSetting?.quoteAsset?.toUpperCase()}
          </div>
        </div>
        <div className="flex justify-between">
          <div className="body-sm-regular-12 text-white-500">Max Buy</div>
          <div className="body-sm-medium-12 flex items-center gap-1">
            <AppNumber
              value={maxBuy}
              decimals={pairSetting?.quantityPrecision}
              isFormatLargeNumber={false}
            />
            {pairSetting?.baseAsset?.toUpperCase()}
          </div>
        </div>
      </div>

      <AppButton
        variant="buy"
        size="large"
        onClick={handlePlaceOrder}
        disabled={isDisableBuyButton}
        isLoading={isLoading}
      >
        Buy
      </AppButton>
    </div>
  );
};
