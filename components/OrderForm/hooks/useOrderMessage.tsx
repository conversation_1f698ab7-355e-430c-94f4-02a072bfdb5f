import { useCallback } from "react";
import { EOrderType } from "..";
import { successMsg } from "@/libs/toast";
import { EOrderSide } from "../OrderFormMobile";
import { EOrderStatus, TOrderAction } from "@/types/order";

type TOrderToast = {
  quantity: string;
  status: EOrderStatus;
  action: TOrderAction;
  baseAsset: string;
  quoteAsset: string;
  side: EOrderSide;
  type: EOrderType;
  errorMessage?: string;
  symbol?: string;
};

export function useOrderToast() {
  const getMessageByAction = (orderDetails: TOrderToast) => {
    switch (orderDetails?.action) {
      case TOrderAction.PLACE:
        return `Submitted exchange ${orderDetails?.type?.toLowerCase()} ${orderDetails?.side?.toLowerCase()} order for ${orderDetails?.quantity?.toUpperCase()} ${orderDetails?.baseAsset?.toUpperCase()} by using ${orderDetails?.quoteAsset.toUpperCase()}`;
      default:
        return "Action successfully";
    }
  };

  return useCallback((orderDetails: TOrderToast) => {
    const message = getMessageByAction(orderDetails);

    successMsg(message);
  }, []);
}
