import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store";
import { TAccountUpdateWsData, TBalance } from "@/types/account";
import {
  AppBroadcast,
  BROADCAST_EVENTS,
  TBroadcastEvent,
} from "@/libs/broadcast";
import { setAccount } from "@/store/account.store";

const useAccountBalance = ({ coin }: { coin?: string }) => {
  const balances = useSelector(
    (state: RootState) => state.account.account?.balances
  );

  const dispatch = useDispatch();
  const [accountBalances, setAccountBalances] = useState<TBalance[] | []>([]);
  const [coinBalance, setCoinBalance] = useState<TBalance>({
    available: "0",
    locked: "0",
    asset: "",
  });

  useEffect(() => {
    const currentBalances = balances || [];

    if (coin) {
      const coinBalance = currentBalances.find(
        (balance) => balance.asset?.toLowerCase() === coin?.toLowerCase()
      );

      if (coinBalance?.asset) {
        setCoinBalance(coinBalance);
      }
    } else {
      setAccountBalances(currentBalances);
    }
  }, [balances, coin]);

  useEffect(() => {
    const handleAccountUpdate = (event: TBroadcastEvent) => {
      const balanceUpdated: TAccountUpdateWsData = JSON.parse(event.detail);

      const currentBalances = balances || [];
      const assetExists = currentBalances.some(
        (item) =>
          item.asset?.toUpperCase() === balanceUpdated.asset?.toUpperCase()
      );

      let accountBalancesUpdated;

      if (assetExists) {
        // Update existing balance
        accountBalancesUpdated = currentBalances.map((item) =>
          item.asset?.toUpperCase() === balanceUpdated.asset?.toUpperCase() &&
          item.operationId > balanceUpdated.operationId
            ? {
                ...item,
                available: balanceUpdated.available,
                locked: balanceUpdated.locked,
                operationId: balanceUpdated.operationId,
              }
            : item
        );
      } else {
        accountBalancesUpdated = [
          ...currentBalances,
          {
            asset: balanceUpdated.asset,
            available: balanceUpdated.available,
            locked: balanceUpdated.locked,
            operationId: balanceUpdated.operationId,
          },
        ];
      }

      dispatch(setAccount({ balances: accountBalancesUpdated }));
    };

    AppBroadcast.on(BROADCAST_EVENTS.ACCOUNT_UPDATED, handleAccountUpdate);

    return () => {
      AppBroadcast.remove(
        BROADCAST_EVENTS.ACCOUNT_UPDATED,
        handleAccountUpdate
      );
    };
  }, [balances, dispatch]);

  return {
    accountBalances,
    coinBalance,
  };
};

export default useAccountBalance;
